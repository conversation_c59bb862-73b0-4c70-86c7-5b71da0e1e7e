import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * POST endpoint to create a multi-KML bulk job
 */
export const POST = withAuth(async (userId, request) => {
  const supabase = createClient();
  const { taskIds, outputFilename } = await request.json(); // Read outputFilename

  if (!Array.isArray(taskIds) || taskIds.length === 0) {
    return NextResponse.json(
      { error: 'Invalid request: taskIds must be a non-empty array' },
      { status: 400 }
    );
  }
  // Validate outputFilename
  if (typeof outputFilename !== 'string' || outputFilename.trim() === '') {
    return NextResponse.json(
      { error: 'Invalid request: outputFilename must be a non-empty string' },
      { status: 400 }
    );
  }
// Define a type for the variable structure
// Define types for variable structures
type VarItem = { name: string; data: any; links?: string[] }; // Allow optional links
type TaskResult = { id: number; file_name: string; file_type: string; content_type: string; file_path: string };
type TaskData = {
  id: number;
  job_id: string | null; // Renamed from batch_id (parent Job ID)
  name: string;
  vars: VarItem[] | null; // Original task vars
  task_results: TaskResult[];
  dataset: { id: number; name: string }[] | null; // Expect array or null based on TS error
};

// Helper function to merge template vars (settings) with aggregated task vars (context)
// Template vars take precedence if names clash.
const mergeVars = (templateVars: VarItem[] | null, aggregatedVars: VarItem[]): VarItem[] => {
  const finalVars: VarItem[] = templateVars ? JSON.parse(JSON.stringify(templateVars)) : []; // Deep copy template vars or start empty
  const templateVarMap = new Map<string, VarItem>(finalVars.map(item => [item.name, item]));

  for (const aggVar of aggregatedVars) {
    if (!templateVarMap.has(aggVar.name)) {
      // Add aggregated var only if it doesn't exist in the template
      finalVars.push(aggVar);
      templateVarMap.set(aggVar.name, aggVar); // Add to map to prevent duplicates if aggVars has them
    }
    // If name exists in template, we keep the template version (precedence)
  }
  return finalVars;
};


  try {
    // 1. Get the template (ID 19)
    const { data: template, error: templateError } = await supabase
      .from('global_job_templates')
      .select('template_data, vars') // Select template_data and vars
      .eq('id', 19)
      .single();

    if (templateError) {
      throw new Error(`Failed to fetch template: ${templateError.message}`);
    }
    // Ensure template.vars is treated as an array, even if null/undefined from DB
    const templateVarsArray: VarItem[] = Array.isArray(template?.vars.vars) ? template.vars.vars : [];
    // Ensure template.vars is treated as an array, even if null/undefined from DB


    // Extract required file types from template (keep existing logic)
    let requiredFileTypes: string[] = [];
    try {
      const templateData = typeof template.template_data === 'string'
        ? JSON.parse(template.template_data)
        : template.template_data;
      if (templateData && templateData.filetypes_required && Array.isArray(templateData.filetypes_required)) {
        requiredFileTypes = templateData.filetypes_required;
      }
    } catch (e) {
      console.error('Error parsing template data:', e);
      requiredFileTypes = ['tix4kml']; // Fallback
    }
    if (requiredFileTypes.length === 0) {
      requiredFileTypes = ['tix4kml']; // Default
    }

    // 2. Get the task information including original 'vars'
    const { data: tasksData, error: tasksError } = await supabase
      .from('tasks') // Renamed jobs to tasks
      .select(`
        id,
        job_id,
        name,
        vars,
        dataset:datasets(id, name),
        task_results(
          id,
          file_name,
          file_type,
          content_type,
          file_path
        )
      `)
      .in('id', taskIds)
      .eq('user_id', userId);

    if (tasksError) {
      throw new Error(`Failed to fetch tasks: ${tasksError.message}`);
    }
    // Cast to the defined type
    const tasks: TaskData[] = tasksData as TaskData[];

    // Verify required file types (keep existing logic, but use the fetched tasks)
    const tasksWithRequiredFiles = tasks.filter(task =>
      requiredFileTypes.every(fileType =>
        task.task_results.some(result => result.file_type === fileType)
      )
    );

    if (tasksWithRequiredFiles.length !== taskIds.length) {
      return NextResponse.json(
        { error: `Some selected tasks do not have the required files: ${requiredFileTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // 3. Aggregate Original Task Variables
    const aggregatedOriginalVarsMap = new Map<string, any[]>();
    tasks.forEach(task => {
      // Fix: Access the nested 'vars' array within the task.vars object
      const taskVarsObject = task.vars as any; // Cast to any to check for nested property
      const originalVars = (taskVarsObject && typeof taskVarsObject === 'object' && Array.isArray(taskVarsObject.vars))
        ? taskVarsObject.vars
        : [];
      originalVars.forEach((variable: VarItem) => { // Add explicit type VarItem
        if (variable && typeof variable === 'object' && variable.name) {
          const currentData = aggregatedOriginalVarsMap.get(variable.name) || [];
          currentData.push(variable.data);
          aggregatedOriginalVarsMap.set(variable.name, currentData);
        }
      });
    });

    // Format aggregated original vars for the new structure
    const formattedAggregatedVars: VarItem[] = [];
    aggregatedOriginalVarsMap.forEach((dataArray, varName) => {
      formattedAggregatedVars.push({
        name: varName,
        links: ["bulk"],
        data: dataArray
      });
    });
    // Format aggregated original vars for the new structure

    // 4. Aggregate Output File IDs for WorkerVars
    // Map<file_type_varName, Map<task_id, file_id[]>>
    const aggregatedOutputFileIdsMap = new Map<string, Map<number, number[]>>();
    tasks.forEach(task => {
      task.task_results.forEach(result => {
        if (result && result.file_type && result.id && task.id) { // Ensure task.id is available
          const varName = `outputfile_${result.file_type}`; // e.g., outputfile_tix4kml

          // Get or create the map for this file type
          let taskToFileIdsMap = aggregatedOutputFileIdsMap.get(varName);
          if (!taskToFileIdsMap) {
            taskToFileIdsMap = new Map<number, number[]>();
            aggregatedOutputFileIdsMap.set(varName, taskToFileIdsMap);
          }

          // Get or create the array for this task within the file type map
          let fileIdsForTask = taskToFileIdsMap.get(task.id);
          if (!fileIdsForTask) {
            fileIdsForTask = [];
            taskToFileIdsMap.set(task.id, fileIdsForTask);
          }

          // Add the current file ID
          fileIdsForTask.push(result.id);
        }
      });
    });

    // Format aggregated file IDs for the new workervars structure
    const finalWorkervars: VarItem[] = [];
    aggregatedOutputFileIdsMap.forEach((taskToFileIdsMap, varName) => {
      const dataArrayForVar: (number | number[])[] = []; // Array can hold numbers or arrays of numbers

      // Iterate through the file ID arrays contributed by each task for this file type
      taskToFileIdsMap.forEach((fileIdsArray) => {
        if (fileIdsArray.length === 1) {
          dataArrayForVar.push(fileIdsArray[0]); // Add single ID as a number
        } else if (fileIdsArray.length > 1) {
          dataArrayForVar.push(fileIdsArray); // Add array of IDs
        }
        // If length is 0 (shouldn't happen with current logic), do nothing
      });

      // Only add the var if there's data
      if (dataArrayForVar.length > 0) {
          finalWorkervars.push({
            name: varName,
            links: ["bulk"],
            data: dataArrayForVar
          });
      }
    });


    // 5. Merge template vars with aggregated original task vars and add/update OUTPUTFILENAME
    let finalVars = mergeVars(templateVarsArray, formattedAggregatedVars);

    // Check if OUTPUTFILENAME already exists and update it, otherwise add it
    const existingVarIndex = finalVars.findIndex(v => v.name === 'OUTPUTFILENAME');
    if (existingVarIndex > -1) {
      finalVars[existingVarIndex].data = outputFilename; // Update existing
    } else {
      finalVars.push({ name: 'OUTPUTFILENAME', data: outputFilename, links: [] }); // Add new
    }
    // 5. Merge template vars with aggregated original task vars and add/update OUTPUTFILENAME

    // Get parent job information
    const jobId = tasks.length > 0 && tasks[0].job_id ? tasks[0].job_id : null; // Renamed batchId to jobId, tasks[0].batch_id to tasks[0].job_id

    // Fetch parent job metadata if jobId exists
    if (jobId) { // Renamed batchId to jobId
      const { data: jobData } = await supabase // Renamed batchData to jobData
        .from('jobs') // Renamed batches to jobs
        .select('name, created_at, description')
        .eq('id', jobId) // Renamed batchId to jobId
        .single();

      if (jobData) { // Renamed batchData to jobData
        // Add parent job metadata to finalVars if data exists
        if (jobData.name) { // Renamed batchData.name to jobData.name
          finalVars.push({
            name: 'JOB_NAME',
            data: jobData.name, // Renamed batchData.name to jobData.name

          });
        }
        if (jobData.created_at) { // Renamed batchData.created_at to jobData.created_at
          finalVars.push({
            name: 'JOB_CREATED_AT',
            data: new Date(jobData.created_at).toISOString(), // Renamed batchData.created_at to jobData.created_at

          });
        }
        if (jobData.description) { // Renamed batchData.description to jobData.description
          finalVars.push({
            name: 'JOB_DESCRIPTION',
            data: jobData.description, // Renamed batchData.description to jobData.description

          });
        }
      } else {
        console.warn(`Parent job with jobId ${jobId} not found, proceeding without parent metadata`); // Renamed batchId to jobId
      }
    }

    // Add JOB_METAINFO with processed timestamp
    const now = new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = String(now.getFullYear());
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const processedTimestamp = `${day}/${month}/${year} ${hours}:${minutes}`;

    finalVars.push({
      name: 'JOB_METAINFO',
      data: {
        processed_timestamp: processedTimestamp
      }
    });


    // 6. Create the new bulk job in the database
    const { data: bulkJob, error: bulkJobError } = await supabase
      .from('tasks') // Renamed jobs to tasks (this "bulk job" is a "bulk task")
      .insert({
        user_id: userId,
        name: `Multi-KML Job (${taskIds.length} tasks)`,
        description: `Bulk job to create multi-KML file from ${taskIds.length} tasks`,
        status: 'queued',
        global_job_template_id: 19,
        job_json: template.template_data,
        vars: { vars: finalVars },
        workervars: finalWorkervars,
        bulk_job_type: 'multi_kml',
        job_id: jobId // Renamed batch_id to job_id, batchId to jobId
      })
      .select('id') // Only select ID is needed here
      .single();

    if (bulkJobError) {
      // Log the actual error for debugging
      console.error("Error inserting bulk job:", bulkJobError);
      throw new Error(`Failed to create bulk job: ${bulkJobError.message}`);
    }

    if (!bulkJob || !bulkJob.id) {
       throw new Error('Bulk job created but failed to retrieve ID.');
    }


    // 7. Create task relationships (updated for new schema)
    const relationships = taskIds.map(taskId => ({
      bulk_task_id: bulkJob.id,
      task_id: taskId
    }));

    const { error: relationshipError } = await supabase
      .from('bulk_task_tasks')
      .insert(relationships);

    if (relationshipError) {
      // Potentially add logic here to roll back the job creation if relationships fail
      console.error("Error inserting bulk task tasks relationships:", relationshipError);
      // Consider deleting the created bulkJob here if relationships are critical
      throw new Error(`Failed to create task relationships: ${relationshipError.message}`);
    }

    return NextResponse.json({ success: true, id: bulkJob.id });
  } catch (error) {
    console.error('Bulk job creation error:', error);
    // Ensure error is an instance of Error before accessing message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred during bulk job creation';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
   );
  }
});
